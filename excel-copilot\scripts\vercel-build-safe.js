#!/usr/bin/env node

/**
 * Script de build seguro para Vercel
 * Resolve problemas com cross-env e configurações de ambiente
 */

const { execSync } = require('child_process');
const path = require('path');

console.log('🚀 Iniciando build seguro para Vercel...');

// Configurar variáveis de ambiente para o build
process.env.NODE_OPTIONS = '--max-old-space-size=4096';
process.env.NODE_ENV = process.env.NODE_ENV || 'production';

// Verificar se estamos no Vercel
const isVercel = process.env.VERCEL === '1' || process.env.CI === 'true';

if (isVercel) {
  console.log('✅ Ambiente Vercel detectado');

  // Configurações específicas para Vercel
  process.env.DISABLE_ESLINT_PLUGIN = 'true';
  process.env.GENERATE_SOURCEMAP = 'false';

  console.log('⚙️ Configurações aplicadas:');
  console.log(`   NODE_OPTIONS: ${process.env.NODE_OPTIONS}`);
  console.log(`   NODE_ENV: ${process.env.NODE_ENV}`);
  console.log(`   DISABLE_ESLINT_PLUGIN: ${process.env.DISABLE_ESLINT_PLUGIN}`);
}

try {
  // Limpar cache de build
  console.log('🧹 Limpando cache de build...');
  execSync('npm run clean:build', {
    stdio: 'inherit',
    cwd: process.cwd(),
  });

  // Executar build do Next.js
  console.log('📦 Executando build do Next.js...');
  execSync('npx next build', {
    stdio: 'inherit',
    cwd: process.cwd(),
    env: {
      ...process.env,
      NODE_OPTIONS: '--max-old-space-size=4096',
    },
  });

  console.log('✅ Build concluído com sucesso!');
} catch (error) {
  console.error('❌ Erro durante o build:', error.message);

  // Tentar build com configurações alternativas
  console.log('🔄 Tentando build com configurações alternativas...');

  try {
    execSync('npx next build', {
      stdio: 'inherit',
      cwd: process.cwd(),
      env: {
        ...process.env,
        NODE_OPTIONS: '--max-old-space-size=8192',
        // SEGURANÇA: Removido DISABLE_ENV_VALIDATION para garantir validação de ambiente
        // em produção. Validação de ambiente é crítica para segurança.
      },
    });

    console.log('✅ Build alternativo concluído com sucesso!');
  } catch (fallbackError) {
    console.error('❌ Build falhou mesmo com configurações alternativas');
    console.error('Erro:', fallbackError.message);
    process.exit(1);
  }
}
