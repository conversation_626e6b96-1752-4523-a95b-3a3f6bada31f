{"functions": {"src/app/api/**/*.ts": {"maxDuration": 30, "memory": 1024}}, "headers": [{"source": "/api/auth/(.*)", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains"}]}, {"source": "/api/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}, {"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains; preload"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Content-Security-Policy", "value": "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline' https://vercel.live https://va.vercel-scripts.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: blob:; connect-src 'self' https: wss:; frame-ancestors 'none';"}]}], "rewrites": [{"source": "/api/:path*", "destination": "/api/:path*"}, {"source": "/health", "destination": "/api/health"}, {"source": "/status", "destination": "/api/health"}], "redirects": [{"source": "/admin", "destination": "/dashboard", "permanent": false}], "buildCommand": "node scripts/secure-deployment.js", "outputDirectory": ".next", "installCommand": "npm ci", "framework": "nextjs", "regions": ["iad1", "sfo1"], "env": {"NODE_ENV": "production", "NEXT_TELEMETRY_DISABLED": "1"}}