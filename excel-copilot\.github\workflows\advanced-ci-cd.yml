name: Advanced CI/CD Pipeline with Security

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '18'
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}

jobs:
  # Job 1: Security Pre-Check
  security-precheck:
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🔒 Secret scanning
        run: |
          echo "🔍 Scanning for hardcoded secrets..."
          
          # Verificar credenciais hardcoded
          if grep -r "sk_live_" src/ || grep -r "pk_live_" src/; then
            echo "❌ Found hardcoded Stripe live keys!"
            exit 1
          fi

          if grep -r "AKIA" src/ || grep -r "aws_secret" src/; then
            echo "❌ Found hardcoded AWS credentials!"
            exit 1
          fi

          if grep -r "ghp_" src/ || grep -r "gho_" src/; then
            echo "❌ Found hardcoded GitHub tokens!"
            exit 1
          fi

          # Verificar padrões de secrets
          if grep -rE "['\"][a-zA-Z0-9]{32,}['\"]" src/ --include="*.ts" --include="*.js"; then
            echo "⚠️ Found potential hardcoded secrets (32+ char strings)"
            echo "Please review the above matches"
          fi

          echo "✅ Secret scanning completed"

      - name: 🔍 Dependency vulnerability scan
        run: |
          echo "🔍 Scanning dependencies for vulnerabilities..."
          npm audit --audit-level high || echo "⚠️ Vulnerabilities found, review required"

  # Job 2: Lint e Type Check
  lint-and-typecheck:
    runs-on: ubuntu-latest
    needs: security-precheck
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🔍 Run ESLint
        run: npm run lint

      - name: 🔧 Run TypeScript check
        run: npm run type-check

      - name: 📊 Generate lint report
        if: always()
        run: |
          npm run lint -- --format json --output-file lint-report.json || true
          echo "## 🔍 Lint Results" >> $GITHUB_STEP_SUMMARY
          if [ -f lint-report.json ]; then
            echo "Lint report generated successfully" >> $GITHUB_STEP_SUMMARY
          fi

  # Job 3: Unit Tests with Coverage
  unit-tests:
    runs-on: ubuntu-latest
    needs: lint-and-typecheck
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🧪 Run unit tests with coverage
        run: npm run test -- --coverage --watchAll=false

      - name: 📊 Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella
          fail_ci_if_error: false

      - name: 📈 Coverage report
        if: always()
        run: |
          echo "## 📊 Test Coverage" >> $GITHUB_STEP_SUMMARY
          if [ -f coverage/coverage-summary.json ]; then
            echo "Coverage report available in artifacts" >> $GITHUB_STEP_SUMMARY
          fi

  # Job 4: Integration Tests
  integration-tests:
    runs-on: ubuntu-latest
    needs: lint-and-typecheck
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🗄️ Setup test database
        run: |
          npm run db:migrate:test
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db

      - name: 🔗 Run integration tests
        run: npm run test:integration
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
          REDIS_URL: redis://localhost:6379

      - name: 🏥 Health check integration
        run: |
          echo "🏥 Testing health check endpoints..."
          npm run health:check || echo "Health checks need review"

  # Job 5: Advanced Security Scanning
  advanced-security:
    runs-on: ubuntu-latest
    needs: lint-and-typecheck
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🔒 Advanced security audit
        run: |
          echo "🔒 Running advanced security checks..."
          
          # Audit de dependências
          npm audit --audit-level moderate --json > audit-report.json || true
          
          # Verificar configurações de segurança
          echo "Checking security configurations..."
          
          # Verificar se CSP está configurado
          if grep -q "Content-Security-Policy" vercel.json; then
            echo "✅ CSP configured"
          else
            echo "⚠️ CSP not found in vercel.json"
          fi
          
          # Verificar headers de segurança
          if grep -q "X-Frame-Options" vercel.json; then
            echo "✅ X-Frame-Options configured"
          else
            echo "⚠️ X-Frame-Options not configured"
          fi

      - name: 🔍 CodeQL Analysis
        uses: github/codeql-action/analyze@v2
        with:
          languages: javascript,typescript

      - name: 🛡️ OWASP ZAP Baseline Scan
        if: github.event_name == 'push'
        uses: zaproxy/action-baseline@v0.7.0
        with:
          target: 'https://excel-copilot-eight.vercel.app'
          rules_file_name: '.zap/rules.tsv'
          cmd_options: '-a'

  # Job 6: Infrastructure Validation
  infrastructure-validation:
    runs-on: ubuntu-latest
    needs: security-precheck
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🏗️ Validate infrastructure configs
        run: |
          echo "🏗️ Validating infrastructure configurations..."
          
          # Validar vercel.json
          if [ -f vercel.json ]; then
            echo "✅ vercel.json found"
            # Validar JSON syntax
            node -e "JSON.parse(require('fs').readFileSync('vercel.json', 'utf8'))" && echo "✅ vercel.json is valid JSON"
          else
            echo "❌ vercel.json not found"
            exit 1
          fi
          
          # Validar Dockerfile se existir
          if [ -f Dockerfile ]; then
            echo "✅ Dockerfile found"
          fi
          
          # Validar docker-compose.yml se existir
          if [ -f docker-compose.yml ]; then
            echo "✅ docker-compose.yml found"
          fi

      - name: 🔧 Validate secret management
        run: |
          echo "🔧 Validating secret management..."
          
          # Verificar se secret manager existe
          if [ -f src/lib/security/secret-manager.ts ]; then
            echo "✅ Secret manager found"
          else
            echo "⚠️ Secret manager not found"
          fi
          
          # Verificar se disaster recovery existe
          if [ -f src/lib/infrastructure/disaster-recovery.ts ]; then
            echo "✅ Disaster recovery system found"
          else
            echo "⚠️ Disaster recovery system not found"
          fi

  # Job 7: Secure Build and Deploy
  secure-deploy:
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests, advanced-security, infrastructure-validation]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🔒 Pre-deployment security check
        run: |
          echo "🔒 Running pre-deployment security checks..."
          
          # Verificar se não há secrets hardcoded
          if grep -r "sk_live_" src/ || grep -r "pk_live_" src/; then
            echo "❌ Found hardcoded secrets in source code!"
            exit 1
          fi
          
          echo "✅ Pre-deployment security check passed"

      - name: 💾 Create backup
        run: |
          echo "💾 Creating pre-deployment backup..."
          node scripts/secure-deployment.js --backup-only || echo "⚠️ Backup creation failed"

      - name: 🏗️ Secure build
        run: |
          echo "🏗️ Running secure build..."
          npm run build
          
          # Verificar se build foi bem-sucedido
          if [ -d .next ]; then
            echo "✅ Build completed successfully"
          else
            echo "❌ Build failed"
            exit 1
          fi

      - name: 🚀 Deploy to Vercel with monitoring
        run: |
          echo "🚀 Deploying to Vercel with monitoring..."
          node scripts/secure-deployment.js
        env:
          VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN }}
          VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
          VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}

      - name: 🏥 Post-deployment health checks
        run: |
          echo "🏥 Running post-deployment health checks..."
          sleep 30
          
          # Health check básico
          curl -f https://excel-copilot-eight.vercel.app/api/health || exit 1
          
          # Health checks específicos
          curl -f https://excel-copilot-eight.vercel.app/api/health/database || echo "⚠️ Database health check failed"
          curl -f https://excel-copilot-eight.vercel.app/api/health/auth || echo "⚠️ Auth health check failed"
          
          echo "✅ Post-deployment health checks completed"

      - name: 📊 Deployment metrics
        if: always()
        run: |
          echo "📊 Collecting deployment metrics..."
          echo "## 🚀 Deployment Summary" >> $GITHUB_STEP_SUMMARY
          echo "- **Status**: ${{ job.status }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Environment**: Production" >> $GITHUB_STEP_SUMMARY
          echo "- **Timestamp**: $(date -u)" >> $GITHUB_STEP_SUMMARY
          echo "- **Commit**: ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY

      - name: 📧 Notify deployment status
        if: always()
        run: |
          if [ "${{ job.status }}" == "success" ]; then
            echo "✅ Deployment successful!"
          else
            echo "❌ Deployment failed!"
          fi
          # Adicionar integração com Slack/Discord aqui
