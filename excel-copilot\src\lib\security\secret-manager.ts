/**
 * 🔐 SISTEMA DE SECRET MANAGEMENT SEGURO
 * 
 * Sistema avançado para gerenciamento seguro de secrets e credenciais
 * com rotação automática, validação e auditoria completa.
 * 
 * Funcionalidades:
 * - Gerenciamento seguro de secrets
 * - Rotação automática de credenciais
 * - Validação de força de secrets
 * - Auditoria de acesso
 * - Fallback para variáveis de ambiente
 * - Criptografia local de secrets
 * 
 * <AUTHOR> Copilot Security Team
 * @version 1.0.0
 * @since 18/06/2025
 */

import { createHash, randomBytes, createCipheriv, createDecipheriv } from 'crypto';
import { z } from 'zod';

// ============================================================================
// TIPOS E INTERFACES
// ============================================================================

export interface SecretConfig {
  name: string;
  value: string;
  encrypted?: boolean;
  rotationInterval?: number; // em dias
  lastRotated?: Date;
  strength?: 'weak' | 'medium' | 'strong';
  category: 'auth' | 'database' | 'api' | 'encryption' | 'webhook' | 'oauth';
  required: boolean;
  environment: 'development' | 'staging' | 'production' | 'all';
}

export interface SecretValidationResult {
  isValid: boolean;
  strength: 'weak' | 'medium' | 'strong';
  issues: string[];
  recommendations: string[];
}

export interface SecretAuditLog {
  secretName: string;
  action: 'read' | 'write' | 'rotate' | 'validate' | 'delete';
  timestamp: Date;
  userId?: string;
  ipAddress?: string;
  success: boolean;
  details?: string;
}

// ============================================================================
// SCHEMAS DE VALIDAÇÃO
// ============================================================================

const SecretConfigSchema = z.object({
  name: z.string().min(1).max(100),
  value: z.string().min(1),
  encrypted: z.boolean().optional().default(false),
  rotationInterval: z.number().min(1).max(365).optional(),
  lastRotated: z.date().optional(),
  strength: z.enum(['weak', 'medium', 'strong']).optional(),
  category: z.enum(['auth', 'database', 'api', 'encryption', 'webhook', 'oauth']),
  required: z.boolean(),
  environment: z.enum(['development', 'staging', 'production', 'all']),
});

// ============================================================================
// CONFIGURAÇÕES DE SECRETS
// ============================================================================

const SECRET_DEFINITIONS: Omit<SecretConfig, 'value'>[] = [
  // Autenticação
  {
    name: 'NEXTAUTH_SECRET',
    category: 'auth',
    required: true,
    environment: 'all',
    rotationInterval: 90,
  },
  {
    name: 'NEXTAUTH_URL',
    category: 'auth',
    required: true,
    environment: 'all',
  },
  
  // OAuth
  {
    name: 'GOOGLE_CLIENT_SECRET',
    category: 'oauth',
    required: true,
    environment: 'production',
    rotationInterval: 180,
  },
  {
    name: 'GITHUB_CLIENT_SECRET',
    category: 'oauth',
    required: true,
    environment: 'production',
    rotationInterval: 180,
  },
  
  // Database
  {
    name: 'DATABASE_URL',
    category: 'database',
    required: true,
    environment: 'all',
  },
  {
    name: 'SUPABASE_SERVICE_ROLE_KEY',
    category: 'database',
    required: true,
    environment: 'production',
    rotationInterval: 90,
  },
  
  // APIs
  {
    name: 'STRIPE_SECRET_KEY',
    category: 'api',
    required: true,
    environment: 'production',
    rotationInterval: 90,
  },
  {
    name: 'GOOGLE_CLOUD_PRIVATE_KEY',
    category: 'api',
    required: true,
    environment: 'production',
    rotationInterval: 365,
  },
  
  // Webhooks
  {
    name: 'STRIPE_WEBHOOK_SECRET',
    category: 'webhook',
    required: true,
    environment: 'production',
    rotationInterval: 90,
  },
  
  // Encryption
  {
    name: 'CACHE_SECRET',
    category: 'encryption',
    required: true,
    environment: 'all',
    rotationInterval: 30,
  },
  {
    name: 'CSRF_SECRET',
    category: 'encryption',
    required: true,
    environment: 'all',
    rotationInterval: 30,
  },
];

// ============================================================================
// CLASSE PRINCIPAL - SECRET MANAGER
// ============================================================================

export class SecretManager {
  private secrets = new Map<string, SecretConfig>();
  private auditLogs: SecretAuditLog[] = [];
  private encryptionKey: string;
  
  constructor() {
    // Gerar chave de criptografia local (em produção, usar KMS)
    this.encryptionKey = process.env.SECRET_ENCRYPTION_KEY || this.generateEncryptionKey();
    this.loadSecretsFromEnvironment();
  }

  // ========================================================================
  // MÉTODOS PÚBLICOS
  // ========================================================================

  /**
   * Obter um secret de forma segura
   */
  async getSecret(name: string, userId?: string, ipAddress?: string): Promise<string | null> {
    try {
      const secret = this.secrets.get(name);
      
      if (!secret) {
        // Fallback para variável de ambiente
        const envValue = process.env[name];
        if (envValue) {
          this.logAudit(name, 'read', true, userId, ipAddress, 'Fallback to environment variable');
          return envValue;
        }
        
        this.logAudit(name, 'read', false, userId, ipAddress, 'Secret not found');
        return null;
      }

      let value = secret.value;
      
      // Descriptografar se necessário
      if (secret.encrypted) {
        value = this.decrypt(value);
      }
      
      this.logAudit(name, 'read', true, userId, ipAddress);
      return value;
      
    } catch (error) {
      this.logAudit(name, 'read', false, userId, ipAddress, error instanceof Error ? error.message : 'Unknown error');
      return null;
    }
  }

  /**
   * Definir um secret de forma segura
   */
  async setSecret(
    name: string, 
    value: string, 
    options: Partial<SecretConfig> = {},
    userId?: string,
    ipAddress?: string
  ): Promise<boolean> {
    try {
      // Validar entrada
      const definition = SECRET_DEFINITIONS.find(def => def.name === name);
      if (!definition) {
        throw new Error(`Secret definition not found for: ${name}`);
      }

      // Validar força do secret
      const validation = this.validateSecretStrength(value, name);
      if (!validation.isValid && definition.required) {
        throw new Error(`Secret validation failed: ${validation.issues.join(', ')}`);
      }

      // Criar configuração do secret
      const secretConfig: SecretConfig = {
        ...definition,
        ...options,
        name,
        value: options.encrypted !== false ? this.encrypt(value) : value,
        encrypted: options.encrypted !== false,
        strength: validation.strength,
        lastRotated: new Date(),
      };

      // Validar com schema
      SecretConfigSchema.parse(secretConfig);

      // Armazenar secret
      this.secrets.set(name, secretConfig);
      
      this.logAudit(name, 'write', true, userId, ipAddress, `Strength: ${validation.strength}`);
      return true;
      
    } catch (error) {
      this.logAudit(name, 'write', false, userId, ipAddress, error instanceof Error ? error.message : 'Unknown error');
      return false;
    }
  }

  /**
   * Validar força de um secret
   */
  validateSecretStrength(value: string, secretName: string): SecretValidationResult {
    const issues: string[] = [];
    const recommendations: string[] = [];
    
    // Validações básicas
    if (value.length < 8) {
      issues.push('Secret muito curto (mínimo 8 caracteres)');
      recommendations.push('Use pelo menos 32 caracteres para secrets críticos');
    }
    
    if (value.length < 32 && secretName.includes('SECRET')) {
      issues.push('Secret de autenticação deve ter pelo menos 32 caracteres');
    }
    
    // Verificar complexidade
    const hasUpperCase = /[A-Z]/.test(value);
    const hasLowerCase = /[a-z]/.test(value);
    const hasNumbers = /\d/.test(value);
    const hasSpecialChars = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(value);
    
    const complexityScore = [hasUpperCase, hasLowerCase, hasNumbers, hasSpecialChars].filter(Boolean).length;
    
    if (complexityScore < 3) {
      issues.push('Secret deve conter pelo menos 3 tipos de caracteres (maiúscula, minúscula, número, especial)');
      recommendations.push('Use geradores de senha seguros para criar secrets');
    }
    
    // Verificar padrões comuns inseguros
    const commonPatterns = ['password', '123456', 'admin', 'secret', 'key'];
    if (commonPatterns.some(pattern => value.toLowerCase().includes(pattern))) {
      issues.push('Secret contém padrões comuns inseguros');
      recommendations.push('Evite palavras comuns e padrões previsíveis');
    }
    
    // Determinar força
    let strength: 'weak' | 'medium' | 'strong' = 'weak';
    if (value.length >= 32 && complexityScore >= 3 && issues.length === 0) {
      strength = 'strong';
    } else if (value.length >= 16 && complexityScore >= 2) {
      strength = 'medium';
    }
    
    return {
      isValid: issues.length === 0,
      strength,
      issues,
      recommendations,
    };
  }

  /**
   * Carregar secrets das variáveis de ambiente
   */
  private loadSecretsFromEnvironment(): void {
    SECRET_DEFINITIONS.forEach(definition => {
      const envValue = process.env[definition.name];
      if (envValue) {
        const validation = this.validateSecretStrength(envValue, definition.name);
        const secretConfig: SecretConfig = {
          ...definition,
          value: envValue,
          encrypted: false,
          strength: validation.strength,
          lastRotated: new Date(),
        };
        this.secrets.set(definition.name, secretConfig);
      }
    });
  }

  /**
   * Gerar chave de criptografia
   */
  private generateEncryptionKey(): string {
    return randomBytes(32).toString('hex');
  }

  /**
   * Criptografar valor
   */
  private encrypt(text: string): string {
    const iv = randomBytes(16);
    const cipher = createCipheriv('aes-256-cbc', Buffer.from(this.encryptionKey, 'hex'), iv);
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return iv.toString('hex') + ':' + encrypted;
  }

  /**
   * Descriptografar valor
   */
  private decrypt(text: string): string {
    const [ivHex, encryptedHex] = text.split(':');
    if (!ivHex || !encryptedHex) {
      throw new Error('Invalid encrypted text format');
    }
    const iv = Buffer.from(ivHex, 'hex');
    const decipher = createDecipheriv('aes-256-cbc', Buffer.from(this.encryptionKey, 'hex'), iv);
    let decrypted = decipher.update(encryptedHex, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  }

  /**
   * Log de auditoria
   */
  private logAudit(
    secretName: string,
    action: SecretAuditLog['action'],
    success: boolean,
    userId?: string,
    ipAddress?: string,
    details?: string
  ): void {
    const auditLog: SecretAuditLog = {
      secretName,
      action,
      timestamp: new Date(),
      userId: userId || undefined,
      ipAddress: ipAddress || undefined,
      success,
      details,
    };
    
    this.auditLogs.push(auditLog);
    
    // Manter apenas os últimos 10000 logs
    if (this.auditLogs.length > 10000) {
      this.auditLogs = this.auditLogs.slice(-10000);
    }
    
    // Log para console em desenvolvimento
    if (process.env.NODE_ENV === 'development') {
      console.log(`[SECRET_AUDIT] ${action} ${secretName}: ${success ? 'SUCCESS' : 'FAILED'}`, details);
    }
  }

  /**
   * Rotacionar um secret automaticamente
   */
  async rotateSecret(name: string, userId?: string, ipAddress?: string): Promise<boolean> {
    try {
      const secret = this.secrets.get(name);
      if (!secret) {
        throw new Error(`Secret not found: ${name}`);
      }

      // Gerar novo valor baseado no tipo de secret
      let newValue: string;

      switch (secret.category) {
        case 'encryption':
          newValue = randomBytes(32).toString('hex');
          break;
        case 'auth':
          newValue = randomBytes(64).toString('base64url');
          break;
        default:
          throw new Error(`Automatic rotation not supported for category: ${secret.category}`);
      }

      // Atualizar secret
      const success = await this.setSecret(name, newValue, {
        ...secret,
        lastRotated: new Date()
      }, userId, ipAddress);

      if (success) {
        this.logAudit(name, 'rotate', true, userId, ipAddress, 'Automatic rotation');
      }

      return success;

    } catch (error) {
      this.logAudit(name, 'rotate', false, userId, ipAddress, error instanceof Error ? error.message : 'Unknown error');
      return false;
    }
  }

  /**
   * Verificar secrets que precisam de rotação
   */
  getSecretsNeedingRotation(): string[] {
    const now = new Date();
    const needsRotation: string[] = [];

    this.secrets.forEach((secret, name) => {
      if (secret.rotationInterval && secret.lastRotated) {
        const daysSinceRotation = Math.floor(
          (now.getTime() - secret.lastRotated.getTime()) / (1000 * 60 * 60 * 24)
        );

        if (daysSinceRotation >= secret.rotationInterval) {
          needsRotation.push(name);
        }
      }
    });

    return needsRotation;
  }

  /**
   * Obter relatório de auditoria
   */
  getAuditReport(limit: number = 100): SecretAuditLog[] {
    return this.auditLogs
      .slice(-limit)
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  /**
   * Validar todos os secrets configurados
   */
  validateAllSecrets(): { [key: string]: SecretValidationResult } {
    const results: { [key: string]: SecretValidationResult } = {};

    this.secrets.forEach((secret, name) => {
      let value = secret.value;
      if (secret.encrypted) {
        try {
          value = this.decrypt(value);
        } catch {
          results[name] = {
            isValid: false,
            strength: 'weak',
            issues: ['Failed to decrypt secret'],
            recommendations: ['Check encryption key and secret integrity'],
          };
          return;
        }
      }

      results[name] = this.validateSecretStrength(value, name);
      this.logAudit(name, 'validate', true, 'system', 'localhost');
    });

    return results;
  }

  /**
   * Obter estatísticas de secrets
   */
  getSecretStats(): {
    total: number;
    byCategory: { [key: string]: number };
    byStrength: { [key: string]: number };
    needingRotation: number;
    encrypted: number;
  } {
    const stats = {
      total: this.secrets.size,
      byCategory: {} as { [key: string]: number },
      byStrength: {} as { [key: string]: number },
      needingRotation: this.getSecretsNeedingRotation().length,
      encrypted: 0,
    };

    this.secrets.forEach(secret => {
      // Por categoria
      stats.byCategory[secret.category] = (stats.byCategory[secret.category] || 0) + 1;

      // Por força
      if (secret.strength) {
        stats.byStrength[secret.strength] = (stats.byStrength[secret.strength] || 0) + 1;
      }

      // Criptografados
      if (secret.encrypted) {
        stats.encrypted++;
      }
    });

    return stats;
  }

  /**
   * Exportar configuração de secrets (sem valores)
   */
  exportSecretConfig(): Omit<SecretConfig, 'value'>[] {
    return Array.from(this.secrets.values()).map(secret => ({
      name: secret.name,
      encrypted: secret.encrypted || false,
      rotationInterval: secret.rotationInterval || undefined,
      lastRotated: secret.lastRotated || undefined,
      strength: secret.strength || undefined,
      category: secret.category,
      required: secret.required,
      environment: secret.environment,
    }));
  }

  /**
   * Verificar se um secret existe
   */
  hasSecret(name: string): boolean {
    return this.secrets.has(name) || !!process.env[name];
  }

  /**
   * Remover um secret
   */
  async deleteSecret(name: string, userId?: string, ipAddress?: string): Promise<boolean> {
    try {
      const secret = this.secrets.get(name);
      if (!secret) {
        throw new Error(`Secret not found: ${name}`);
      }

      if (secret.required) {
        throw new Error(`Cannot delete required secret: ${name}`);
      }

      this.secrets.delete(name);
      this.logAudit(name, 'delete', true, userId, ipAddress);
      return true;

    } catch (error) {
      this.logAudit(name, 'delete', false, userId, ipAddress, error instanceof Error ? error.message : 'Unknown error');
      return false;
    }
  }

  /**
   * Gerar novo secret com força específica
   */
  generateSecret(length: number = 32, includeSpecialChars: boolean = true): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const specialChars = '!@#$%^&*()_+-=[]{}|;:,.<>?';
    const allChars = includeSpecialChars ? chars + specialChars : chars;

    let result = '';
    for (let i = 0; i < length; i++) {
      result += allChars.charAt(Math.floor(Math.random() * allChars.length));
    }

    return result;
  }
}

// ============================================================================
// FUNÇÕES UTILITÁRIAS
// ============================================================================

/**
 * Verificar se um valor parece ser um secret
 */
export function looksLikeSecret(value: string): boolean {
  // Padrões comuns de secrets
  const secretPatterns = [
    /^sk_live_[a-zA-Z0-9]{24,}$/, // Stripe live key
    /^pk_live_[a-zA-Z0-9]{24,}$/, // Stripe publishable key
    /^AKIA[0-9A-Z]{16}$/, // AWS Access Key
    /^[a-zA-Z0-9+/]{40,}={0,2}$/, // Base64 encoded (40+ chars)
    /^[a-fA-F0-9]{32,}$/, // Hex encoded (32+ chars)
    /^ghp_[a-zA-Z0-9]{36}$/, // GitHub Personal Access Token
    /^gho_[a-zA-Z0-9]{36}$/, // GitHub OAuth Token
  ];

  return secretPatterns.some(pattern => pattern.test(value)) ||
         (value.length >= 32 && !/\s/.test(value)); // Long string without spaces
}

/**
 * Sanitizar valor para logs (mascarar secrets)
 */
export function sanitizeForLogs(value: string): string {
  if (looksLikeSecret(value)) {
    if (value.length <= 8) {
      return '*'.repeat(value.length);
    }
    return value.substring(0, 4) + '*'.repeat(value.length - 8) + value.substring(value.length - 4);
  }
  return value;
}

// ============================================================================
// INSTÂNCIA SINGLETON
// ============================================================================

export const secretManager = new SecretManager();
