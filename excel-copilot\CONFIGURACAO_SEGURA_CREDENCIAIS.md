# 🔒 CONFIGURAÇÃO SEGURA DE CREDENCIAIS - EXCEL COPILOT

## ⚠️ AVISO DE SEGURANÇA CRÍTICO

**NUNCA** commite credenciais reais no repositório Git. Este documento explica como configurar credenciais de forma segura.

## 📋 ARQUIVOS REMOVIDOS POR SEGURANÇA

Os seguintes arquivos foram removidos do repositório por conterem credenciais reais:
- `.env.local` (credenciais de desenvolvimento)
- `.next/standalone/.env.production` (credenciais de produção)

## 🛡️ CONFIGURAÇÃO SEGURA

### 1. Para Desenvolvimento Local

Copie o arquivo `.env.example` para `.env.local`:

```bash
cp .env.example .env.local
```

Edite `.env.local` e configure suas credenciais de desenvolvimento:

```bash
# NUNCA use credenciais de produção em desenvolvimento!
AUTH_NEXTAUTH_SECRET="[GERAR_COM_openssl_rand_-base64_32]"
AUTH_GOOGLE_CLIENT_ID="[SEU_GOOGLE_CLIENT_ID_DEV]"
AUTH_GOOGLE_CLIENT_SECRET="[SEU_GOOGLE_CLIENT_SECRET_DEV]"
# ... outras credenciais
```

### 2. Para Produção

Configure as variáveis de ambiente diretamente no Vercel:

```bash
# Use o Vercel CLI ou dashboard web
vercel env add AUTH_NEXTAUTH_SECRET
vercel env add AUTH_GOOGLE_CLIENT_SECRET
# ... outras credenciais
```

## 🔐 GERANDO CREDENCIAIS SEGURAS

### NextAuth Secret
```bash
openssl rand -base64 32
```

### Cache Secret
```bash
openssl rand -hex 32
```

## 📝 CHECKLIST DE SEGURANÇA

- [ ] Arquivo `.env.local` não está no Git
- [ ] Arquivo `.env.production` não está no Git
- [ ] Credenciais de produção estão apenas no Vercel
- [ ] Credenciais de desenvolvimento são diferentes das de produção
- [ ] Secrets têm pelo menos 32 caracteres
- [ ] Não há credenciais hardcoded no código

## 🚨 EM CASO DE VAZAMENTO

Se credenciais foram expostas:

1. **IMEDIATAMENTE** revogue todas as credenciais expostas
2. Gere novas credenciais
3. Atualize as configurações de produção
4. Monitore logs de acesso suspeito
5. Considere notificar usuários se necessário

## 📖 DOCUMENTAÇÃO ADICIONAL

- [Configuração OAuth Google](./docs/oauth-google-setup.md)
- [Configuração Stripe](./docs/stripe-setup.md)
- [Configuração Supabase](./docs/supabase-setup.md)
