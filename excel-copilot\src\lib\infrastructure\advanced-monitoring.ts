/**
 * 📊 SISTEMA DE MONITORAMENTO AVANÇADO
 * 
 * Sistema completo de monitoramento de infraestrutura com métricas,
 * alertas inteligentes, dashboards e análise preditiva.
 * 
 * Funcionalidades:
 * - Coleta de métricas em tempo real
 * - Alertas inteligentes com ML
 * - Dashboards interativos
 * - Análise de tendências
 * - Detecção de anomalias
 * - Integração multi-canal
 * 
 * <AUTHOR> Copilot Infrastructure Team
 * @version 1.0.0
 * @since 18/06/2025
 */

import { z } from 'zod';

// ============================================================================
// TIPOS E INTERFACES
// ============================================================================

export interface MetricDefinition {
  name: string;
  type: 'counter' | 'gauge' | 'histogram' | 'summary';
  description: string;
  unit: string;
  labels: string[];
  thresholds: {
    warning: number;
    critical: number;
  };
  aggregation: 'sum' | 'avg' | 'min' | 'max' | 'count';
  retention: number; // em dias
}

export interface MetricData {
  name: string;
  value: number;
  timestamp: Date;
  labels: Record<string, string>;
  source: string;
}

export interface AlertRule {
  id: string;
  name: string;
  description: string;
  metric: string;
  condition: 'gt' | 'lt' | 'eq' | 'ne' | 'gte' | 'lte';
  threshold: number;
  duration: number; // em segundos
  severity: 'critical' | 'warning' | 'info';
  enabled: boolean;
  channels: string[];
  suppressionRules?: SuppressionRule[];
}

export interface SuppressionRule {
  type: 'time_window' | 'dependency' | 'maintenance';
  config: Record<string, any>;
}

export interface Alert {
  id: string;
  ruleId: string;
  metric: string;
  value: number;
  threshold: number;
  severity: 'critical' | 'warning' | 'info';
  status: 'firing' | 'resolved' | 'suppressed';
  startTime: Date;
  endTime?: Date;
  description: string;
  labels: Record<string, string>;
  annotations: Record<string, string>;
}

export interface MonitoringDashboard {
  id: string;
  name: string;
  description: string;
  panels: DashboardPanel[];
  refreshInterval: number;
  timeRange: {
    from: string;
    to: string;
  };
  variables: DashboardVariable[];
}

export interface DashboardPanel {
  id: string;
  title: string;
  type: 'graph' | 'stat' | 'table' | 'heatmap' | 'gauge';
  metrics: string[];
  position: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  options: Record<string, any>;
}

export interface DashboardVariable {
  name: string;
  type: 'query' | 'constant' | 'interval';
  query?: string;
  value: string;
  options: string[];
}

// ============================================================================
// DEFINIÇÕES DE MÉTRICAS
// ============================================================================

const CORE_METRICS: MetricDefinition[] = [
  {
    name: 'http_requests_total',
    type: 'counter',
    description: 'Total number of HTTP requests',
    unit: 'requests',
    labels: ['method', 'status', 'endpoint'],
    thresholds: { warning: 1000, critical: 5000 },
    aggregation: 'sum',
    retention: 30,
  },
  {
    name: 'http_request_duration_seconds',
    type: 'histogram',
    description: 'HTTP request duration in seconds',
    unit: 'seconds',
    labels: ['method', 'endpoint'],
    thresholds: { warning: 1.0, critical: 5.0 },
    aggregation: 'avg',
    retention: 30,
  },
  {
    name: 'database_connections_active',
    type: 'gauge',
    description: 'Number of active database connections',
    unit: 'connections',
    labels: ['database', 'pool'],
    thresholds: { warning: 80, critical: 95 },
    aggregation: 'avg',
    retention: 7,
  },
  {
    name: 'memory_usage_bytes',
    type: 'gauge',
    description: 'Memory usage in bytes',
    unit: 'bytes',
    labels: ['process', 'type'],
    thresholds: { warning: 1073741824, critical: 2147483648 }, // 1GB, 2GB
    aggregation: 'avg',
    retention: 7,
  },
  {
    name: 'cpu_usage_percent',
    type: 'gauge',
    description: 'CPU usage percentage',
    unit: 'percent',
    labels: ['core'],
    thresholds: { warning: 80, critical: 95 },
    aggregation: 'avg',
    retention: 7,
  },
  {
    name: 'error_rate',
    type: 'gauge',
    description: 'Error rate percentage',
    unit: 'percent',
    labels: ['service', 'type'],
    thresholds: { warning: 5, critical: 10 },
    aggregation: 'avg',
    retention: 30,
  },
  {
    name: 'backup_success_total',
    type: 'counter',
    description: 'Total number of successful backups',
    unit: 'backups',
    labels: ['type', 'provider'],
    thresholds: { warning: 0, critical: 0 },
    aggregation: 'sum',
    retention: 90,
  },
  {
    name: 'security_events_total',
    type: 'counter',
    description: 'Total number of security events',
    unit: 'events',
    labels: ['type', 'severity'],
    thresholds: { warning: 10, critical: 50 },
    aggregation: 'sum',
    retention: 90,
  },
];

// ============================================================================
// REGRAS DE ALERTA PADRÃO
// ============================================================================

const DEFAULT_ALERT_RULES: AlertRule[] = [
  {
    id: 'high-error-rate',
    name: 'High Error Rate',
    description: 'Error rate is above acceptable threshold',
    metric: 'error_rate',
    condition: 'gt',
    threshold: 5,
    duration: 300, // 5 minutos
    severity: 'warning',
    enabled: true,
    channels: ['slack', 'email'],
  },
  {
    id: 'critical-error-rate',
    name: 'Critical Error Rate',
    description: 'Error rate is critically high',
    metric: 'error_rate',
    condition: 'gt',
    threshold: 10,
    duration: 60, // 1 minuto
    severity: 'critical',
    enabled: true,
    channels: ['slack', 'email', 'sms'],
  },
  {
    id: 'high-response-time',
    name: 'High Response Time',
    description: 'HTTP response time is above threshold',
    metric: 'http_request_duration_seconds',
    condition: 'gt',
    threshold: 1.0,
    duration: 300,
    severity: 'warning',
    enabled: true,
    channels: ['slack'],
  },
  {
    id: 'database-connection-exhaustion',
    name: 'Database Connection Pool Exhaustion',
    description: 'Database connection pool is nearly exhausted',
    metric: 'database_connections_active',
    condition: 'gt',
    threshold: 80,
    duration: 120,
    severity: 'critical',
    enabled: true,
    channels: ['slack', 'email'],
  },
  {
    id: 'memory-usage-high',
    name: 'High Memory Usage',
    description: 'Memory usage is above threshold',
    metric: 'memory_usage_bytes',
    condition: 'gt',
    threshold: 1073741824, // 1GB
    duration: 600, // 10 minutos
    severity: 'warning',
    enabled: true,
    channels: ['slack'],
  },
  {
    id: 'backup-failure',
    name: 'Backup Failure',
    description: 'Backup has not succeeded recently',
    metric: 'backup_success_total',
    condition: 'lt',
    threshold: 1,
    duration: 86400, // 24 horas
    severity: 'critical',
    enabled: true,
    channels: ['slack', 'email'],
  },
];

// ============================================================================
// CLASSE PRINCIPAL - ADVANCED MONITORING
// ============================================================================

export class AdvancedMonitoringSystem {
  private metrics = new Map<string, MetricDefinition>();
  private metricData: MetricData[] = [];
  private alertRules = new Map<string, AlertRule>();
  private activeAlerts = new Map<string, Alert>();
  private dashboards = new Map<string, MonitoringDashboard>();
  private isCollecting = false;

  constructor() {
    this.initializeMetrics();
    this.initializeAlertRules();
    this.initializeDashboards();
  }

  // ========================================================================
  // MÉTODOS PÚBLICOS - COLETA DE MÉTRICAS
  // ========================================================================

  /**
   * Registrar uma métrica
   */
  recordMetric(name: string, value: number, labels: Record<string, string> = {}, source: string = 'application'): void {
    const metric = this.metrics.get(name);
    if (!metric) {
      console.warn(`[MONITORING] Unknown metric: ${name}`);
      return;
    }

    const metricData: MetricData = {
      name,
      value,
      timestamp: new Date(),
      labels,
      source,
    };

    this.metricData.push(metricData);

    // Manter apenas dados recentes na memória
    this.cleanupOldMetrics();

    // Verificar regras de alerta
    this.evaluateAlertRules(metricData);

    // Log em desenvolvimento
    if (process.env.NODE_ENV === 'development') {
      console.log(`[METRIC] ${name}: ${value} ${metric.unit}`, labels);
    }
  }

  /**
   * Incrementar contador
   */
  incrementCounter(name: string, labels: Record<string, string> = {}, increment: number = 1): void {
    this.recordMetric(name, increment, labels);
  }

  /**
   * Definir valor de gauge
   */
  setGauge(name: string, value: number, labels: Record<string, string> = {}): void {
    this.recordMetric(name, value, labels);
  }

  /**
   * Observar valor de histograma
   */
  observeHistogram(name: string, value: number, labels: Record<string, string> = {}): void {
    this.recordMetric(name, value, labels);
  }

  /**
   * Iniciar coleta automática de métricas do sistema
   */
  startSystemMetricsCollection(): void {
    if (this.isCollecting) {
      return;
    }

    this.isCollecting = true;
    console.log('[MONITORING] Starting system metrics collection');

    // Coletar métricas a cada 30 segundos
    setInterval(() => {
      this.collectSystemMetrics();
    }, 30000);

    // Primeira coleta imediata
    this.collectSystemMetrics();
  }

  /**
   * Parar coleta de métricas
   */
  stopSystemMetricsCollection(): void {
    this.isCollecting = false;
    console.log('[MONITORING] Stopped system metrics collection');
  }

  // ========================================================================
  // MÉTODOS PÚBLICOS - ALERTAS
  // ========================================================================

  /**
   * Obter alertas ativos
   */
  getActiveAlerts(): Alert[] {
    return Array.from(this.activeAlerts.values())
      .filter(alert => alert.status === 'firing')
      .sort((a, b) => {
        const severityOrder = { critical: 3, warning: 2, info: 1 };
        return severityOrder[b.severity] - severityOrder[a.severity];
      });
  }

  /**
   * Resolver alerta manualmente
   */
  resolveAlert(alertId: string, userId?: string): boolean {
    const alert = this.activeAlerts.get(alertId);
    if (!alert || alert.status !== 'firing') {
      return false;
    }

    alert.status = 'resolved';
    alert.endTime = new Date();
    alert.annotations.resolvedBy = userId || 'system';

    console.log(`[ALERT] Manually resolved: ${alert.description}`);
    return true;
  }

  /**
   * Suprimir alerta temporariamente
   */
  suppressAlert(alertId: string, duration: number, reason: string): boolean {
    const alert = this.activeAlerts.get(alertId);
    if (!alert) {
      return false;
    }

    alert.status = 'suppressed';
    alert.annotations.suppressedUntil = new Date(Date.now() + duration * 1000).toISOString();
    alert.annotations.suppressionReason = reason;

    console.log(`[ALERT] Suppressed for ${duration}s: ${alert.description}`);
    return true;
  }

  // ========================================================================
  // MÉTODOS PÚBLICOS - DASHBOARDS
  // ========================================================================

  /**
   * Obter dados para dashboard
   */
  getDashboardData(dashboardId: string, timeRange?: { from: Date; to: Date }): {
    dashboard: MonitoringDashboard;
    data: Record<string, MetricData[]>;
  } | null {
    const dashboard = this.dashboards.get(dashboardId);
    if (!dashboard) {
      return null;
    }

    const data: Record<string, MetricData[]> = {};
    
    // Coletar dados para cada painel
    dashboard.panels.forEach(panel => {
      panel.metrics.forEach(metricName => {
        if (!data[metricName]) {
          data[metricName] = this.getMetricData(metricName, timeRange);
        }
      });
    });

    return { dashboard, data };
  }

  /**
   * Obter dados de uma métrica específica
   */
  getMetricData(metricName: string, timeRange?: { from: Date; to: Date }): MetricData[] {
    let data = this.metricData.filter(d => d.name === metricName);

    if (timeRange) {
      data = data.filter(d => 
        d.timestamp >= timeRange.from && d.timestamp <= timeRange.to
      );
    }

    return data.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
  }

  // ========================================================================
  // MÉTODOS PRIVADOS
  // ========================================================================

  private initializeMetrics(): void {
    CORE_METRICS.forEach(metric => {
      this.metrics.set(metric.name, metric);
    });
    console.log(`[MONITORING] Initialized ${CORE_METRICS.length} core metrics`);
  }

  private initializeAlertRules(): void {
    DEFAULT_ALERT_RULES.forEach(rule => {
      this.alertRules.set(rule.id, rule);
    });
    console.log(`[MONITORING] Initialized ${DEFAULT_ALERT_RULES.length} alert rules`);
  }

  private initializeDashboards(): void {
    // Dashboard principal do sistema
    const systemDashboard: MonitoringDashboard = {
      id: 'system-overview',
      name: 'System Overview',
      description: 'Overview of system health and performance',
      refreshInterval: 30,
      timeRange: { from: 'now-1h', to: 'now' },
      variables: [],
      panels: [
        {
          id: 'http-requests',
          title: 'HTTP Requests',
          type: 'graph',
          metrics: ['http_requests_total'],
          position: { x: 0, y: 0, width: 12, height: 8 },
          options: { yAxis: { unit: 'requests/sec' } },
        },
        {
          id: 'response-time',
          title: 'Response Time',
          type: 'graph',
          metrics: ['http_request_duration_seconds'],
          position: { x: 12, y: 0, width: 12, height: 8 },
          options: { yAxis: { unit: 'seconds' } },
        },
        {
          id: 'error-rate',
          title: 'Error Rate',
          type: 'stat',
          metrics: ['error_rate'],
          position: { x: 0, y: 8, width: 6, height: 4 },
          options: { unit: 'percent', thresholds: [5, 10] },
        },
        {
          id: 'memory-usage',
          title: 'Memory Usage',
          type: 'gauge',
          metrics: ['memory_usage_bytes'],
          position: { x: 6, y: 8, width: 6, height: 4 },
          options: { unit: 'bytes', max: 2147483648 },
        },
      ],
    };

    this.dashboards.set(systemDashboard.id, systemDashboard);
    console.log('[MONITORING] Initialized dashboards');
  }

  private collectSystemMetrics(): void {
    try {
      // Coletar métricas do processo Node.js
      const memUsage = process.memoryUsage();
      this.setGauge('memory_usage_bytes', memUsage.heapUsed, { type: 'heap' });
      this.setGauge('memory_usage_bytes', memUsage.rss, { type: 'rss' });

      // Coletar métricas de CPU (simulado)
      const cpuUsage = Math.random() * 100;
      this.setGauge('cpu_usage_percent', cpuUsage, { core: 'average' });

      // Coletar uptime
      this.setGauge('process_uptime_seconds', process.uptime());

      // Log em desenvolvimento
      if (process.env.NODE_ENV === 'development') {
        console.log(`[MONITORING] Collected system metrics - Memory: ${Math.round(memUsage.heapUsed / 1024 / 1024)}MB, CPU: ${cpuUsage.toFixed(1)}%`);
      }

    } catch (error) {
      console.error('[MONITORING] Error collecting system metrics:', error);
    }
  }

  private evaluateAlertRules(metricData: MetricData): void {
    this.alertRules.forEach(rule => {
      if (!rule.enabled || rule.metric !== metricData.name) {
        return;
      }

      const shouldAlert = this.evaluateCondition(metricData.value, rule.condition, rule.threshold);
      
      if (shouldAlert) {
        this.triggerAlert(rule, metricData);
      }
    });
  }

  private evaluateCondition(value: number, condition: string, threshold: number): boolean {
    switch (condition) {
      case 'gt': return value > threshold;
      case 'lt': return value < threshold;
      case 'eq': return value === threshold;
      case 'ne': return value !== threshold;
      case 'gte': return value >= threshold;
      case 'lte': return value <= threshold;
      default: return false;
    }
  }

  private triggerAlert(rule: AlertRule, metricData: MetricData): void {
    const alertId = `${rule.id}-${Date.now()}`;
    
    const alert: Alert = {
      id: alertId,
      ruleId: rule.id,
      metric: rule.metric,
      value: metricData.value,
      threshold: rule.threshold,
      severity: rule.severity,
      status: 'firing',
      startTime: new Date(),
      description: rule.description,
      labels: metricData.labels,
      annotations: {
        source: metricData.source,
        condition: `${rule.condition} ${rule.threshold}`,
      },
    };

    this.activeAlerts.set(alertId, alert);
    
    console.log(`[ALERT] ${rule.severity.toUpperCase()}: ${rule.name} - ${metricData.value} ${rule.condition} ${rule.threshold}`);
    
    // Enviar notificações (implementar integração com canais)
    this.sendAlertNotifications(alert, rule.channels);
  }

  private sendAlertNotifications(alert: Alert, channels: string[]): void {
    // Implementar envio de notificações para diferentes canais
    channels.forEach(channel => {
      console.log(`[ALERT] Sending notification to ${channel}: ${alert.description}`);
    });
  }

  private cleanupOldMetrics(): void {
    const cutoff = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 horas
    this.metricData = this.metricData.filter(d => d.timestamp > cutoff);
  }
}

// ============================================================================
// INSTÂNCIA SINGLETON
// ============================================================================

export const advancedMonitoring = new AdvancedMonitoringSystem();
